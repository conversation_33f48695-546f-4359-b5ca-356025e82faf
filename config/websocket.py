import json


def is_id_online(id_value):
    """
    判断ID是否在线的逻辑
    这里使用简单的规则：如果是数字，偶数为在线，奇数为离线
    如果是字符串，根据长度判断：长度为偶数为在线，奇数为离线
    你可以根据实际需求修改这个方法
    """
    if str(id_value).isdigit():
        return int(id_value) % 2 == 0
    else:
        return len(str(id_value)) % 2 == 0


async def websocket_application(scope, receive, send):
    while True:
        event = await receive()

        if event["type"] == "websocket.connect":
            await send({"type": "websocket.accept"})

        if event["type"] == "websocket.disconnect":
            break

        if event["type"] == "websocket.receive":
            if event["text"] == "ping":
                await send({"type": "websocket.send", "text": "pong!"})
            else:
                try:
                    # 解析接收到的JSON数据
                    data = json.loads(event["text"])

                    if data.get("action") == "check_ids":
                        ids_input = data.get("ids_input", "")

                        online_ids = []
                        offline_ids = []

                        if ids_input:
                            # 解析输入的ID，参照form_valid的逻辑
                            lines = ids_input.strip().split('\n')
                            for line in lines:
                                line = line.strip()
                                if line:  # 只要不是空行就处理
                                    if is_id_online(line):
                                        online_ids.append(line)
                                    else:
                                        offline_ids.append(line)

                        # 发送结果回客户端
                        response = {
                            "action": "check_ids_result",
                            "online_ids": online_ids,
                            "offline_ids": offline_ids,
                            "status": "success"
                        }

                        await send({
                            "type": "websocket.send",
                            "text": json.dumps(response)
                        })

                except json.JSONDecodeError:
                    # 如果不是有效的JSON，发送错误消息
                    error_response = {
                        "action": "error",
                        "message": "Invalid JSON format",
                        "status": "error"
                    }
                    await send({
                        "type": "websocket.send",
                        "text": json.dumps(error_response)
                    })
                except Exception as e:
                    # 处理其他异常
                    error_response = {
                        "action": "error",
                        "message": str(e),
                        "status": "error"
                    }
                    await send({
                        "type": "websocket.send",
                        "text": json.dumps(error_response)
                    })
