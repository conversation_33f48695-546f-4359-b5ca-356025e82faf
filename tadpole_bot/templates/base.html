
{% load static i18n compress%}<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}
<html lang="{{ LANGUAGE_CODE }}" data-bs-theme="dark">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>
      {% block title %}
      Tadpole Bot
    {% endblock title %}
  </title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description"
        content="Behold My Awesome Project!" />
  <meta name="author"
        content="<PERSON>" />
  <link rel="icon" href="{% static 'images/favicons/favicon.ico' %}" />
  {% block css %}
    <!-- Latest compiled and minified Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
<!-- Your stuff: Third-party CSS libraries go here -->
<!-- This file stores project-specific CSS -->


{% compress css %}
  <link href="{% static 'css/project.css' %}" rel="stylesheet" />
{% endcompress %}


{% endblock css %}
<!-- Le javascript
    ================================================== -->
{# Placed at the top of the document so pages load faster with defer #}
{% block javascript %}


<!-- Bootstrap JS -->
<script defer src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.min.js" integrity="sha384-7qAoOXltbVP82dhxHAUje59V5r2YsVfBafyUDxEdApLPmcdhBPg1DKg1ERo0BZlK" crossorigin="anonymous"></script>
<!-- Your stuff: Third-party javascript libraries go here -->


<!-- place project specific Javascript in this file -->


{% compress js %}
  <script defer src="{% static 'js/project.js' %}"></script>
{% endcompress %}


{% endblock javascript %}
</head>
<body class="{% block bodyclass %}{% endblock bodyclass %}">
  {% block body %}
  <div class="mb-1">
    <nav class="navbar navbar-expand-md">
      <div class="container-fluid">
        <button class="navbar-toggler navbar-toggler-right"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent"
                aria-expanded="false"
                aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <a class="navbar-brand" href="{% url 'home' %}">Tadpole Bot</a>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <ul class="navbar-nav mr-auto">
            <li class="nav-item active">
              <a class="nav-link" href="{% url 'home' %}">Home <span class="visually-hidden">(current)</span></a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'about' %}">About</a>
            </li>
            {% if request.user.is_authenticated %}
              <li class="nav-item">
                <a class="nav-link"
                   href="{% url 'users:detail' request.user.pk %}">{% translate "My Profile" %}</a>
              </li>
              <li class="nav-item">
                {# URL provided by django-allauth/account/urls.py #}
                <a class="nav-link" href="{% url 'account_logout' %}">{% translate "Sign Out" %}</a>
              </li>
            {% else %}
              {% if ACCOUNT_ALLOW_REGISTRATION %}
                <li class="nav-item">
                  {# URL provided by django-allauth/account/urls.py #}
                  <a id="sign-up-link" class="nav-link" href="{% url 'account_signup' %}">{% translate "Sign Up" %}</a>
                </li>
              {% endif %}
              <li class="nav-item">
                {# URL provided by django-allauth/account/urls.py #}
                <a id="log-in-link" class="nav-link" href="{% url 'account_login' %}">{% translate "Sign In" %}</a>
              </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>
  </div>
  <div class="container">
    {% if messages %}
      {% for message in messages %}
        <div class="alert alert-dismissible {% if message.tags %}alert-{{ message.tags }}{% endif %}">
          {{ message }}
          <button type="button"
                  class="btn-close"
                  data-bs-dismiss="alert"
                  aria-label="Close"></button>
        </div>
      {% endfor %}
    {% endif %}
    {% block main %}
      {% block content %}
        <p>Use this document as a way to quick start any new project.</p>
      {% endblock content %}
    {% endblock main %}

  </div>
  {% endblock body %}
  <!-- /container -->
  {% block modal %}
  {% endblock modal %}
  {% block inline_javascript %}
    {% comment %}
    Script tags with only code, no src (defer by default). To run
    with a "defer" so that you run inline code:
    <script>
      window.addEventListener('DOMContentLoaded', () => {
        /* Run whatever you want */
      });
    </script>
    {% endcomment %}
  {% endblock inline_javascript %}
</body>
</html>
