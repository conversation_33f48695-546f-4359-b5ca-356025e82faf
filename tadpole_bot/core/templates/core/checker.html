{% extends 'base.html' %}

{% block title %}ID在线状态检查器{% endblock title %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">ID在线状态检查器</h2>
        </div>
    </div>

    <!-- 输出区域 -->
    <div class="row mb-4">
        <!-- 在线ID显示 -->
        <div class="col-md-6 mb-3">
            <label for="online-ids" class="form-label">
                <i class="text-success">●</i> 在线ID
            </label>
            <textarea
                id="online-ids"
                class="form-control"
                rows="4"
                readonly
                placeholder="在线的ID将显示在这里..."
                style="background-color: #f8f9fa;">{% if online_ids %}{% for id in online_ids %}{{ id }}
{% endfor %}{% endif %}</textarea>
        </div>

        <!-- 离线ID显示 -->
        <div class="col-md-6 mb-3">
            <label for="offline-ids" class="form-label">
                <i class="text-danger">●</i> 离线ID
            </label>
            <textarea
                id="offline-ids"
                class="form-control"
                rows="4"
                readonly
                placeholder="离线的ID将显示在这里..."
                style="background-color: #f8f9fa;">{% if offline_ids %}{% for id in offline_ids %}{{ id }}
{% endfor %}{% endif %}</textarea>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="alert alert-success">
                <strong>在线数量:</strong> <span id="online-count">{{ online_ids|length|default:0 }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="alert alert-danger">
                <strong>离线数量:</strong> <span id="offline-count">{{ offline_ids|length|default:0 }}</span>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="row">
        <div class="col-12">
            <form method="post" id="checker-form">
                {% csrf_token %}
                <div class="mb-3">
                    <label class="form-label">
                        <i class="text-primary">●</i> 输入ID
                    </label>

                    <!-- 三个并列的输入框 -->
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="{{ form.id_input_1.id_for_label }}" class="form-label">ID输入框1</label>
                            {{ form.id_input_1 }}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.id_input_2.id_for_label }}" class="form-label">ID输入框2</label>
                            {{ form.id_input_2 }}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.id_input_3.id_for_label }}" class="form-label">ID输入框3</label>
                            {{ form.id_input_3 }}
                        </div>
                    </div>

                    <div class="form-text mt-2">
                        请在上面的三个输入框中输入ID。系统会自动判断每个ID的在线状态。
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-secondary me-md-2" id="clear-btn">
                        清空
                    </button>
                    <button type="submit" class="btn btn-primary">
                        检查状态
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 使用说明:</h6>
                <ul class="mb-0">
                    <li>在下方三个输入框中输入ID</li>
                    <li>点击"检查状态"按钮或输入时自动检查</li>
                    <li>在线ID会显示在左上方的绿色区域</li>
                    <li>离线ID会显示在右上方的红色区域</li>
                    <li>当前规则：数字ID偶数为在线，奇数为离线；字符串ID长度偶数为在线，奇数为离线（可根据需要修改）</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checker-form');
    const idInput1 = document.getElementById('id-input-1');
    const idInput2 = document.getElementById('id-input-2');
    const idInput3 = document.getElementById('id-input-3');
    const onlineIds = document.getElementById('online-ids');
    const offlineIds = document.getElementById('offline-ids');
    const onlineCount = document.getElementById('online-count');
    const offlineCount = document.getElementById('offline-count');
    const clearBtn = document.getElementById('clear-btn');

    // 清空按钮功能
    clearBtn.addEventListener('click', function() {
        idInput1.value = '';
        idInput2.value = '';
        idInput3.value = '';
        onlineIds.value = '';
        offlineIds.value = '';
        onlineCount.textContent = '0';
        offlineCount.textContent = '0';
    });

    // 实时检查功能（可选）
    let timeoutId;
    function setupInputListener(input) {
        input.addEventListener('input', function() {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(function() {
                checkIds();
            }, 500); // 500ms延迟，避免频繁请求
        });
    }

    setupInputListener(idInput1);
    setupInputListener(idInput2);
    setupInputListener(idInput3);

    function checkIds() {
        const ids = {
            'id_input_1': idInput1.value,
            'id_input_2': idInput2.value,
            'id_input_3': idInput3.value
        };

        // 检查是否有任何输入
        const hasInput = Object.values(ids).some(value => value.trim());
        if (!hasInput) {
            onlineIds.value = '';
            offlineIds.value = '';
            onlineCount.textContent = '0';
            offlineCount.textContent = '0';
            return;
        }

        fetch('/checker/ajax/check-numbers/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ids: ids
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                onlineIds.value = data.online_ids.join('\n');
                offlineIds.value = data.offline_ids.join('\n');
                onlineCount.textContent = data.online_ids.length;
                offlineCount.textContent = data.offline_ids.length;
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
});
</script>
{% endblock %}
