{% extends 'base.html' %}

{% block title %}数字在线状态检查器{% endblock title %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">数字在线状态检查器</h2>
        </div>
    </div>

    <!-- 输出区域 -->
    <div class="row mb-4">
        <!-- 在线数字显示 -->
        <div class="col-md-6 mb-3">
            <label for="online-numbers" class="form-label">
                <i class="text-success">●</i> 在线数字
            </label>
            <textarea
                id="online-numbers"
                class="form-control"
                rows="8"
                readonly
                placeholder="在线的数字将显示在这里..."
                style="background-color: #f8f9fa;">{% if online_numbers %}{% for num in online_numbers %}{{ num }}
{% endfor %}{% endif %}</textarea>
        </div>

        <!-- 离线数字显示 -->
        <div class="col-md-6 mb-3">
            <label for="offline-numbers" class="form-label">
                <i class="text-danger">●</i> 离线数字
            </label>
            <textarea
                id="offline-numbers"
                class="form-control"
                rows="8"
                readonly
                placeholder="离线的数字将显示在这里..."
                style="background-color: #f8f9fa;">{% if offline_numbers %}{% for num in offline_numbers %}{{ num }}
{% endfor %}{% endif %}</textarea>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="row mb-3">
        <div class="col-md-6">
            <div class="alert alert-success">
                <strong>在线数量:</strong> <span id="online-count">{{ online_numbers|length|default:0 }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="alert alert-danger">
                <strong>离线数量:</strong> <span id="offline-count">{{ offline_numbers|length|default:0 }}</span>
            </div>
        </div>
    </div>

    <!-- 输入区域 -->
    <div class="row">
        <div class="col-12">
            <form method="post" id="checker-form">
                {% csrf_token %}
                <div class="mb-3">
                    <label for="{{ form.numbers_input.id_for_label }}" class="form-label">
                        <i class="text-primary">●</i> 输入数字 (每行一个)
                    </label>
                    {{ form.numbers_input }}
                    <div class="form-text">
                        请在上面的文本框中输入数字，每行一个数字。系统会自动判断每个数字的在线状态。
                    </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-secondary me-md-2" id="clear-btn">
                        清空
                    </button>
                    <button type="submit" class="btn btn-primary">
                        检查状态
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> 使用说明:</h6>
                <ul class="mb-0">
                    <li>在下方输入框中输入数字，每行一个</li>
                    <li>点击"检查状态"按钮或输入时自动检查</li>
                    <li>在线数字会显示在左上方的绿色区域</li>
                    <li>离线数字会显示在右上方的红色区域</li>
                    <li>当前规则：偶数为在线，奇数为离线（可根据需要修改）</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('checker-form');
    const numbersInput = document.getElementById('numbers-input');
    const onlineNumbers = document.getElementById('online-numbers');
    const offlineNumbers = document.getElementById('offline-numbers');
    const onlineCount = document.getElementById('online-count');
    const offlineCount = document.getElementById('offline-count');
    const clearBtn = document.getElementById('clear-btn');

    // 清空按钮功能
    clearBtn.addEventListener('click', function() {
        numbersInput.value = '';
        onlineNumbers.value = '';
        offlineNumbers.value = '';
        onlineCount.textContent = '0';
        offlineCount.textContent = '0';
    });

    // 实时检查功能（可选）
    let timeoutId;
    numbersInput.addEventListener('input', function() {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(function() {
            checkNumbers();
        }, 500); // 500ms延迟，避免频繁请求
    });

    function checkNumbers() {
        const numbers = numbersInput.value;
        if (!numbers.trim()) {
            onlineNumbers.value = '';
            offlineNumbers.value = '';
            onlineCount.textContent = '0';
            offlineCount.textContent = '0';
            return;
        }

        fetch('/checker/ajax/check-numbers/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                numbers: numbers
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                onlineNumbers.value = data.online_numbers.join('\n');
                offlineNumbers.value = data.offline_numbers.join('\n');
                onlineCount.textContent = data.online_numbers.length;
                offlineCount.textContent = data.offline_numbers.length;
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
});
</script>
{% endblock %}
