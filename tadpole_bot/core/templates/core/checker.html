{% extends 'base.html' %}

{% block title %}数字检查器{% endblock title %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <h2 class="text-center mb-4">数字检查器</h2>

            <!-- 结果显示区域 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="correctNumbers" class="form-label">正确的数字:</label>
                    <textarea id="correctNumbers" class="form-control" rows="8" readonly
                              placeholder="正确的数字将显示在这里..."></textarea>
                </div>
                <div class="col-md-6">
                    <label for="incorrectNumbers" class="form-label">错误的数字:</label>
                    <textarea id="incorrectNumbers" class="form-control" rows="8" readonly
                              placeholder="错误的数字将显示在这里..."></textarea>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="row mb-3">
                <div class="col-12 text-center">
                    <button id="processBtn" class="btn btn-primary me-2">处理数字</button>
                    <button id="clearBtn" class="btn btn-secondary">清空</button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="row">
                <div class="col-12">
                    <label for="numberInput" class="form-label">输入数字 (每行一个数字):</label>
                    <textarea id="numberInput" class="form-control" rows="10"
                              placeholder="请在这里输入数字，每行一个数字...&#10;例如:&#10;123&#10;456&#10;789"></textarea>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %}

{% block inline_javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const numberInput = document.getElementById('numberInput');
    const correctNumbers = document.getElementById('correctNumbers');
    const incorrectNumbers = document.getElementById('incorrectNumbers');
    const processBtn = document.getElementById('processBtn');
    const clearBtn = document.getElementById('clearBtn');

    // 处理数字的函数 - 这里可以根据你的具体需求修改判断逻辑
    function isCorrectNumber(num) {
        // 示例逻辑：偶数为正确，奇数为错误
        // 你可以根据实际需求修改这个函数
        return num % 2 === 0;
    }

    // 处理按钮点击事件
    processBtn.addEventListener('click', function() {
        const inputText = numberInput.value.trim();
        if (!inputText) {
            alert('请先输入一些数字！');
            return;
        }

        const lines = inputText.split('\n');
        const correct = [];
        const incorrect = [];

        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (trimmedLine) {
                const num = parseFloat(trimmedLine);
                if (!isNaN(num)) {
                    if (isCorrectNumber(num)) {
                        correct.push(trimmedLine);
                    } else {
                        incorrect.push(trimmedLine);
                    }
                } else {
                    // 非数字内容放入错误列表
                    incorrect.push(trimmedLine + ' (非数字)');
                }
            }
        });

        correctNumbers.value = correct.join('\n');
        incorrectNumbers.value = incorrect.join('\n');
    });

    // 清空按钮点击事件
    clearBtn.addEventListener('click', function() {
        numberInput.value = '';
        correctNumbers.value = '';
        incorrectNumbers.value = '';
    });

    // 实时处理选项（可选）
    // 如果你想要实时处理，可以取消下面的注释
    /*
    numberInput.addEventListener('input', function() {
        // 延迟处理，避免频繁计算
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {
            processBtn.click();
        }, 500);
    });
    */
});
</script>
{% endblock inline_javascript %}
