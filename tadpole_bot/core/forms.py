from django import forms


class IdCheckerForm(forms.Form):
    id_input_1 = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入ID...',
            'id': 'id-input-1'
        }),
        required=False,
        label='ID输入框1'
    )

    id_input_2 = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入ID...',
            'id': 'id-input-2'
        }),
        required=False,
        label='ID输入框2'
    )

    id_input_3 = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入ID...',
            'id': 'id-input-3'
        }),
        required=False,
        label='ID输入框3'
    )