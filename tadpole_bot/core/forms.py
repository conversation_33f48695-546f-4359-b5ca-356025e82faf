from django import forms
from django.core.exceptions import ValidationError


class NumberCheckerForm(forms.Form):
    """
    表单用于数字检查器功能
    """
    number_input = forms.CharField(
        label="输入数字 (每行一个数字)",
        widget=forms.Textarea(attrs={
            'rows': 10,
            'placeholder': '请在这里输入数字，每行一个数字...\n例如:\n123\n456\n789',
            'class': 'form-control'
        }),
        help_text="请输入数字，每行一个数字",
        required=True
    )

    def clean_number_input(self):
        """
        验证输入的数字
        """
        data = self.cleaned_data['number_input']
        if not data.strip():
            raise ValidationError("请输入至少一个数字")
        return data

    def process_numbers(self):
        """
        处理输入的数字，分离正确和错误的数字
        返回: (correct_numbers, incorrect_numbers)
        """
        if not self.is_valid():
            return [], []

        input_text = self.cleaned_data['number_input']
        lines = input_text.split('\n')
        correct = []
        incorrect = []

        for line in lines:
            trimmed_line = line.strip()
            if trimmed_line:
                try:
                    num = float(trimmed_line)
                    if self.is_correct_number(num):
                        correct.append(trimmed_line)
                    else:
                        incorrect.append(trimmed_line)
                except ValueError:
                    # 非数字内容放入错误列表
                    incorrect.append(f"{trimmed_line} (非数字)")

        return correct, incorrect

    def is_correct_number(self, num):
        """
        判断数字是否正确的逻辑
        默认逻辑：偶数为正确，奇数为错误
        可以根据需要修改这个方法
        """
        return num % 2 == 0


class NumberResultForm(forms.Form):
    """
    用于显示结果的表单
    """
    correct_numbers = forms.CharField(
        label="正确的数字",
        widget=forms.Textarea(attrs={
            'rows': 8,
            'readonly': True,
            'placeholder': '正确的数字将显示在这里...',
            'class': 'form-control'
        }),
        required=False
    )

    incorrect_numbers = forms.CharField(
        label="错误的数字",
        widget=forms.Textarea(attrs={
            'rows': 8,
            'readonly': True,
            'placeholder': '错误的数字将显示在这里...',
            'class': 'form-control'
        }),
        required=False
    )

    def __init__(self, correct_list=None, incorrect_list=None, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if correct_list:
            self.fields['correct_numbers'].initial = '\n'.join(correct_list)

        if incorrect_list:
            self.fields['incorrect_numbers'].initial = '\n'.join(incorrect_list)