from django import forms


class IdCheckerForm(forms.Form):
    ids_input = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control bg-light bg-opacity-55',
            'rows': 10,
            'placeholder': '请输入ID，每行一个ID...',
            'id': 'ids-input'
        }),
        required=False,
        label='输入ID'
    )

    def clean_ids_input(self):
        super().clean()
        id_list = list(
            int(i.strip()) for i in self.cleaned_data['ids_input'].split() if
            i.strip() and i.isdigit() and 8 <= len(i) <= 12
        )
        return id_list
