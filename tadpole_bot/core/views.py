from django.shortcuts import render
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import re

from .forms import IdChecker<PERSON>orm


# Create your views here.

class CheckerView(TemplateView):
    template_name = 'core/checker.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = IdCheckerForm()
        return context

    def get(self, request, *args, **kwargs):
        return render(request, self.template_name, self.get_context_data())

    def post(self, request, *args, **kwargs):
        form = IdCheckerForm(request.POST)
        online_ids = []
        offline_ids = []

        if form.is_valid():
            # 收集所有输入的ID
            all_ids = []
            for field_name in ['id_input_1', 'id_input_2', 'id_input_3']:
                id_value = form.cleaned_data[field_name]
                if id_value and id_value.strip():
                    # 支持多种格式：可以是数字或字符串
                    id_value = id_value.strip()
                    all_ids.append(id_value)

            # 检查每个ID的在线状态
            for id_value in all_ids:
                if self.is_id_online(id_value):
                    online_ids.append(id_value)
                else:
                    offline_ids.append(id_value)

        context = self.get_context_data()
        context.update({
            'form': form,
            'online_ids': online_ids,
            'offline_ids': offline_ids,
        })

        return render(request, self.template_name, context)

    def is_id_online(self, id_value):
        """
        判断ID是否在线的逻辑
        这里使用简单的规则：如果是数字，偶数为在线，奇数为离线
        如果是字符串，根据长度判断：长度为偶数为在线，奇数为离线
        你可以根据实际需求修改这个方法
        """
        if id_value.isdigit():
            return int(id_value) % 2 == 0
        else:
            return len(id_value) % 2 == 0


@method_decorator(csrf_exempt, name='dispatch')
class CheckNumbersAjaxView(TemplateView):
    """AJAX接口用于实时检查数字状态"""

    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            id_inputs = data.get('ids', {})

            online_ids = []
            offline_ids = []

            # 处理三个输入框的数据
            for field_name in ['id_input_1', 'id_input_2', 'id_input_3']:
                id_value = id_inputs.get(field_name, '')
                if id_value and id_value.strip():
                    id_value = id_value.strip()
                    if self.is_id_online(id_value):
                        online_ids.append(id_value)
                    else:
                        offline_ids.append(id_value)

            return JsonResponse({
                'online_ids': online_ids,
                'offline_ids': offline_ids,
                'status': 'success'
            })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })

    def is_id_online(self, id_value):
        """判断ID是否在线的逻辑"""
        if id_value.isdigit():
            return int(id_value) % 2 == 0
        else:
            return len(id_value) % 2 == 0
