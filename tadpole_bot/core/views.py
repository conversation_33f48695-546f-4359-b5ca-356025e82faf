from django.shortcuts import render
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
import re

from .forms import NumberCheckerForm


# Create your views here.

class CheckerView(TemplateView):
    template_name = 'core/checker.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = NumberCheckerForm()
        return context

    def get(self, request, *args, **kwargs):
        return render(request, self.template_name, self.get_context_data())

    def post(self, request, *args, **kwargs):
        form = NumberCheckerForm(request.POST)
        online_numbers = []
        offline_numbers = []

        if form.is_valid():
            numbers_input = form.cleaned_data['numbers_input']
            if numbers_input:
                # 解析输入的数字
                lines = numbers_input.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line.isdigit():
                        number = int(line)
                        # 这里是判断在线/离线的逻辑
                        # 目前使用简单的规则：偶数为在线，奇数为离线
                        # 你可以根据实际需求修改这个逻辑
                        if self.is_number_online(number):
                            online_numbers.append(number)
                        else:
                            offline_numbers.append(number)

        context = self.get_context_data()
        context.update({
            'form': form,
            'online_numbers': online_numbers,
            'offline_numbers': offline_numbers,
        })

        return render(request, self.template_name, context)

    def is_number_online(self, number):
        """
        判断数字是否在线的逻辑
        这里使用简单的规则：偶数为在线，奇数为离线
        你可以根据实际需求修改这个方法
        """
        return number % 2 == 0


@method_decorator(csrf_exempt, name='dispatch')
class CheckNumbersAjaxView(TemplateView):
    """AJAX接口用于实时检查数字状态"""

    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            numbers_input = data.get('numbers', '')

            online_numbers = []
            offline_numbers = []

            if numbers_input:
                lines = numbers_input.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and line.isdigit():
                        number = int(line)
                        if self.is_number_online(number):
                            online_numbers.append(number)
                        else:
                            offline_numbers.append(number)

            return JsonResponse({
                'online_numbers': online_numbers,
                'offline_numbers': offline_numbers,
                'status': 'success'
            })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            })

    def is_number_online(self, number):
        """判断数字是否在线的逻辑"""
        return number % 2 == 0
