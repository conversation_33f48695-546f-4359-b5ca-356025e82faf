from asgiref.sync import async_to_sync
from django.shortcuts import render
from django.views import generic

from .checker import bulk_check_sid
from .forms import IdCheckerForm


class CheckerView(generic.FormView):
    template_name = 'core/checker.html'
    form_class = IdCheckerForm

    def form_valid(self, form):
        cleaned_ids_input = form.cleaned_data['ids_input']
        online_ids, offline_ids, fail_ids = async_to_sync(bulk_check_sid)(cleaned_ids_input)
        # online_ids = [str(i) for i in cleaned_ids_input if i % 2 == 0]
        # offline_ids = [str(i) for i in cleaned_ids_input if i % 2 != 0]
        context = self.get_context_data()
        context.update({
            # 'form': form,
            'online_ids': online_ids,
            'offline_ids': offline_ids,
            'fail_ids': fail_ids
        })
        return render(self.request, self.template_name, context=context)
